<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="15">
				<div class="layout-padding-auto layout-padding-view">
					<el-input v-model="state.queryForm.phone" placeholder="请通过条码扫码录入" clearable style="width: 95%" />
					<el-row class="mt3 mb5">
						<div style="width: 100%; display: flex; justify-content: space-between; align-items: center">
							<div><span>读取标签数：</span><span class="text-blue-500">{{ count.length }}</span></div>
							<div>
								<el-button text type="primary" icon="delete" @click="()=>{count=[]}"> </el-button>
							</div>
						</div>
					</el-row>
					<ul style="overflow: auto; height: 80vh">
						<li v-for="i in count" :key="i" class="infinite-list-item">{{ i }}</li>
					</ul>
					<el-row class="mt12">
						<div style="width: 100%; display: flex; justify-content: space-evenly; align-items: center">
							<el-button type="primary">读标签 </el-button>
							<el-button color="#FF7D00" type="primary" style="color: white !important" @click="matchClick">匹配 </el-button>
						</div>
					</el-row>
				</div>
			</pane>
			<!-- 右侧 -->
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row class="mt7">
						<div class="text-xl font-bold">新增入库</div>
					</el-row>
					<el-row class="mt20 mb10">
						<el-form ref="queryRef" :inline="false" label-width="100px">
							<el-form-item label="入库仓库">
								<el-select placeholder="请选择入库仓库" clearable v-model="Rightname" class="!w-[180px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
						</el-form>
					</el-row>
					<el-table
						height="calc(100vh - 270px)"
						:data="tableData"
						border
						empty-text="暂无数据，请从左侧添加入库信息"
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
						ref="tableRefs"
						style="width: 100%"
					>
						<el-table-column label="序号" type="index" width="60" fixed="left" />
						<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column label="数量" prop="materialNum" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="条码数量" prop="d" show-overflow-tooltip width="260"></el-table-column>
						<el-table-column label="条码明细" show-overflow-tooltip width="250" class-name="custom-class">
							<template #header>
								<div>
									<span>条码明细</span>
									<el-tooltip class="box-item" effect="light" content="全部收起" placement="top-start">
										<el-button
											v-if="tableData.some((row:any) => needsExpansion(row.barCode))"
											link
											type="primary"
											size="small"
											@click="toggleAllExpand"
										>
											<img :src="zdImg" v-show="isAllExpanded" />
										</el-button>
									</el-tooltip>
								</div>
							</template>
							<template #default="scope">
								<div v-if="scope.row.barCode">
									<div class="flex justify-items-start">
										<div>
											<div v-for="(code, index) in getDisplayCodes(scope.row)" :key="index">
												{{ code }}
											</div>
										</div>

										<el-button
											v-if="needsExpansion(scope.row.barCode)"
											link
											type="primary"
											size="small"
											@click="toggleExpand(scope.row, scope.$index)"
										>
											<img :src="scope.row.isExpanded ? zdImg : zkImg" :alt="scope.row.isExpanded ? '收起' : '展开'" />
										</el-button>
									</div>
								</div>
							</template>
						</el-table-column>
					</el-table>
					<el-row>
						<div class="w-full mt15">
							<div class="float-left">物资清单（当前条码数量 {{ tableData.reduce((sum: any, item: any) => sum + item.d, 0) }} ）</div>
							<div class="float-right">
								<el-button @click="returnClick">{{ $t('common.cancelButtonText') }}</el-button>
								<el-button @click="confirmClick" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
							</div>
						</div>
					</el-row>
				</div>
			</pane>
		</splitpanes>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const warnType = ref([
	{ label: '仓库1', value: 1 },
	{ label: '仓库2', value: 2 },
	{ label: '仓库3', value: 3 },
]);
const { tableStyle } = useTable(state);

//确认btn
const loading = ref(false);

//右侧列表数据
let rightDataList = ref<any>([]);
//接收单物资子组件传递的数据
const addToRightList = (row: any, arr?: any[]) => {
	const itemsToAdd = arr || [row];
	const materialCodeMap = new Map(rightDataList.value.map((item: any) => [item.materialCode, item]));

	itemsToAdd.forEach((item: any) => {
		const existingItem: any = materialCodeMap.get(item.materialCode);
		if (existingItem) {
			existingItem.materialNum += item.materialNum;
		} else {
			rightDataList.value.push({ ...item, date: '' });
		}
	});
};
//新增组合名称
let Rightname = ref('');

//右侧提交
const confirmClick = () => {
	if (Rightname.value == '') {
		useMessage().error('请选择入库仓库');
		return;
	}

	loading.value = true;
	setTimeout(() => {
		loading.value = false;
		useMessage().success('添加成功');
	}, 1000);
};

//返回上级菜单
const router = useRouter();
const returnClick = () => {
	router.replace({
		path: '/storage/warehouseManagement/other/index',
	});
};

const tableData = ref<any>([]);
//匹配事件
const matchClick = () => {
	if (!count.value.length) return useMessage().error('请先读取标签');
	const obj = count.value.reduce(
		(acc: any, item) => ((acc[item.slice(12, 18)] = acc[item.slice(12, 18)] ? [...acc[item.slice(12, 18)], item] : [item]), acc),
		{}
	);

	tableData.value.forEach((item: any) => {
		const newBarCode = obj[item.materialCode];
		if (newBarCode) {
			const currentBarCodes = item.barCode ? item.barCode.split(',') : [];
			item.barCode = [...new Set([...currentBarCodes, ...newBarCode])].join(',');
		}
	});
};

tableData.value = [
	{
		materialCode: '000106',
		materialIdentify: 'ABC123',
		materialName: '示例物资',
		materialNum: 10,
		basicUnit: '个',
		d: 5,
		barCode: '',
	},
	{
		materialCode: '000105',
		materialIdentify: 'ABCqqq',
		materialName: '哈哈哈',
		materialNum: 10,
		basicUnit: '个',
		d: 3,
		barCode: '202302280001000105020006',
	},
	{
		materialCode: '000104',
		materialIdentify: 'ccc',
		materialName: '弟弟',
		materialNum: 10,
		basicUnit: '个',
		d: 3,
		barCode: '202302280001000104020006,202302280001000104020009,202302280001000104020001',
	},
].map((item) => ({ ...item, isExpanded: false }));

const count = ref([
	'202302280001000106020003',
	'202302280001000106020002',
	'202302280001000106020001',

	'202302280001000105020003',
	'202302280001000105020004',
	'202302280001000105020005',
	'202302280001000105020006',
	'202302280001000105020007',
	'202302280001000105020008',

	'202302280001000104020003',
	'202302280001000104020004',
	'202302280001000104020005',
	'202302280001000104020006',
]);

// 表格内展开折叠
import zkImg from '/@/assets/flod.png';
import zdImg from '/@/assets/open.png';
const MAX_DISPLAY_LINES = 2;
//table ref
const tableRefs = ref();
//表格数据
//全部数据，截取最大行数   截取字段为row.barCode
const getDisplayCodes = (row: any) => {
	const barCode = row.barCode;
	if (!barCode) return [];
	const codes = barCode.split(',');
	if (codes.length > MAX_DISPLAY_LINES && !row.isExpanded) {
		return [...codes.slice(0, MAX_DISPLAY_LINES - 1)];
	}
	return codes;
};
//table滚动到指定行
const scrollToRow = (tableRef: any, rowIndex: number) => {
	nextTick(() => {
		if (tableRef.value) {
			// @ts-ignore
			const tableBody = tableRef.value.$el.querySelector('.el-table__body-wrapper');
			if (tableBody) {
				const row = tableBody.querySelectorAll('.el-table__row')[rowIndex - 1];
				if (row) {
					row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
				}
			}
		}
	});
};
//将所有行 收起
const toggleAllExpand = () => {
	tableData.value.forEach((row: any) => {
		row.isExpanded = false;
	});
};
//有展开的行  则展示表头收起图标
const isAllExpanded = computed(() => tableData.value.some((row: any) => row.isExpanded));
//判断长度需要展开字段的数据长度
const needsExpansion = (barCode?: string) => {
	return barCode ? barCode.split(',').length > MAX_DISPLAY_LINES : false;
};
//行内展开收起事件
const toggleExpand = (row: any, rowIndex: any) => {
	row.isExpanded = !row.isExpanded;
	if (!row.isExpanded) scrollToRow(tableRefs, rowIndex + 1);
};
</script>
<style lang="scss"></style>
